#!/usr/bin/env python3
"""
测试OmniSharp分析结果

检查OmniSharp分析返回的数据结构
"""

import asyncio
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.csharp_assistant.interfaces.language_server.omnisharp_client import OmniSharpClient
from src.csharp_assistant.config.config import get_config

async def test_omnisharp_analysis():
    """测试OmniSharp分析"""
    print("🔍 测试OmniSharp分析结果...")
    
    try:
        config = get_config()
        client = OmniSharpClient()
        
        # 启动OmniSharp
        await client.start(config.project_path)
        
        # 测试分析文件
        test_files = [
            "E:\\Github\\4.2.1\\Game-6\\Scripts\\Island.cs",
            "E:\\Github\\4.2.1\\Game-6\\Scripts\\Game\\Game.cs", 
            "E:\\Github\\4.2.1\\Game-6\\Scripts\\Global\\EventBus.cs"
        ]
        
        for file_path in test_files:
            print(f"\n📄 分析文件: {file_path}")
            
            result = await client.analyze_code(file_path)
            
            print(f"分析结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 统计数据
            classes = result.get("classes", [])
            methods = result.get("methods", [])
            fields = result.get("fields", [])
            
            print(f"统计: {len(classes)} 个类, {len(methods)} 个方法, {len(fields)} 个字段")
            
            # 详细信息
            for cls in classes:
                print(f"  类: {cls.get('name', 'Unknown')}")
                cls_methods = cls.get("methods", [])
                cls_fields = cls.get("fields", [])
                print(f"    方法: {len(cls_methods)} 个")
                for method in cls_methods:
                    print(f"      - {method.get('name', 'Unknown')}")
                print(f"    字段: {len(cls_fields)} 个")
                for field in cls_fields:
                    print(f"      - {field.get('name', 'Unknown')}")
        
        # 停止OmniSharp
        await client.stop()
        
        print("\n✅ OmniSharp分析测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    await test_omnisharp_analysis()

if __name__ == "__main__":
    asyncio.run(main())
