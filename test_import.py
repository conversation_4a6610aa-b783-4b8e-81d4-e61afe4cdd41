#!/usr/bin/env python3
"""
测试导入
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    print("1. 测试基础导入...")
    from src.csharp_assistant.core.domain.semantic_model import SemanticModel, BaseSymbol, TypeSymbol
    print("✅ 语义模型导入成功")
    
    print("2. 测试创建基础符号...")
    from src.csharp_assistant.core.domain.semantic_model import SymbolKind, AccessModifier, Location
    
    location = Location(
        file_path="test.cs",
        start_line=1,
        start_column=1,
        end_line=1,
        end_column=10
    )
    
    symbol = BaseSymbol(
        id="test-id",
        name="TestClass",
        full_name="TestNamespace.TestClass",
        kind=SymbolKind.CLASS,
        access_modifier=AccessModifier.PUBLIC,
        location=location
    )
    print("✅ 基础符号创建成功")
    
    print("3. 测试语义分析器导入...")
    from src.csharp_assistant.core.services.semantic_analyzer import SemanticAnalyzer
    print("✅ 语义分析器导入成功")
    
    print("4. 测试 HTTP 客户端导入...")
    from src.csharp_assistant.interfaces.language_server.omnisharp_http_client import OmniSharpHttpClient
    print("✅ HTTP 客户端导入成功")
    
    print("\n🎉 所有导入测试通过！")
    
except Exception as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
