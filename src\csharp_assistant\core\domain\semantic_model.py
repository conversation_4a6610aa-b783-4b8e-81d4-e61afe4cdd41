"""
语义模型数据结构

定义用于存储代码语义分析结果的数据结构
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Set, Any
from enum import Enum


class SymbolKind(Enum):
    """符号类型枚举"""
    NAMESPACE = "namespace"
    CLASS = "class"
    INTERFACE = "interface"
    STRUCT = "struct"
    ENUM = "enum"
    METHOD = "method"
    PROPERTY = "property"
    FIELD = "field"
    EVENT = "event"
    CONSTRUCTOR = "constructor"
    DESTRUCTOR = "destructor"
    OPERATOR = "operator"
    INDEXER = "indexer"
    DELEGATE = "delegate"
    PARAMETER = "parameter"
    LOCAL_VARIABLE = "local_variable"


class AccessModifier(Enum):
    """访问修饰符枚举"""
    PUBLIC = "public"
    PRIVATE = "private"
    PROTECTED = "protected"
    INTERNAL = "internal"
    PROTECTED_INTERNAL = "protected internal"
    PRIVATE_PROTECTED = "private protected"


@dataclass
class Location:
    """代码位置信息"""
    file_path: str = ""
    start_line: int = 0
    start_column: int = 0
    end_line: int = 0
    end_column: int = 0


@dataclass
class TypeInfo:
    """类型信息"""
    name: str = ""
    full_name: str = ""
    namespace: str = ""
    is_generic: bool = False
    generic_parameters: List[str] = field(default_factory=list)
    is_nullable: bool = False
    is_array: bool = False
    array_rank: int = 0


@dataclass
class Parameter:
    """参数信息"""
    name: str = ""
    type_info: Optional[TypeInfo] = None
    is_optional: bool = False
    default_value: Optional[str] = None
    is_params: bool = False
    is_ref: bool = False
    is_out: bool = False
    is_in: bool = False


@dataclass
class SymbolReference:
    """符号引用信息"""
    symbol_id: str = ""
    location: Optional[Location] = None
    reference_type: str = "reference"  # "definition", "reference", "write", "read"


@dataclass
class BaseSymbol:
    """基础符号信息"""
    id: str = ""
    name: str = ""
    full_name: str = ""
    kind: SymbolKind = SymbolKind.CLASS
    access_modifier: AccessModifier = AccessModifier.PUBLIC
    location: Optional[Location] = None
    documentation: Optional[str] = None
    attributes: List[str] = field(default_factory=list)
    is_static: bool = False
    is_abstract: bool = False
    is_virtual: bool = False
    is_override: bool = False
    is_sealed: bool = False
    references: List[SymbolReference] = field(default_factory=list)


@dataclass
class NamespaceSymbol(BaseSymbol):
    """命名空间符号"""
    child_namespaces: List[str] = field(default_factory=list)
    types: List[str] = field(default_factory=list)


@dataclass
class TypeSymbol(BaseSymbol):
    """类型符号（类、接口、结构体、枚举等）"""
    namespace: str = ""
    base_types: List[str] = field(default_factory=list)
    implemented_interfaces: List[str] = field(default_factory=list)
    generic_parameters: List[str] = field(default_factory=list)
    members: List[str] = field(default_factory=list)
    nested_types: List[str] = field(default_factory=list)
    is_partial: bool = False
    is_generic: bool = False


@dataclass
class MethodSymbol(BaseSymbol):
    """方法符号"""
    return_type: Optional[TypeInfo] = None
    parameters: List[Parameter] = field(default_factory=list)
    generic_parameters: List[str] = field(default_factory=list)
    is_async: bool = False
    is_extension: bool = False
    is_partial: bool = False
    is_extern: bool = False
    calls: List[str] = field(default_factory=list)  # 调用的其他方法的ID
    called_by: List[str] = field(default_factory=list)  # 被哪些方法调用


@dataclass
class PropertySymbol(BaseSymbol):
    """属性符号"""
    type_info: Optional[TypeInfo] = None
    has_getter: bool = True
    has_setter: bool = True
    is_auto_property: bool = False
    getter_access: Optional[AccessModifier] = None
    setter_access: Optional[AccessModifier] = None


@dataclass
class FieldSymbol(BaseSymbol):
    """字段符号"""
    type_info: Optional[TypeInfo] = None
    is_readonly: bool = False
    is_const: bool = False
    is_volatile: bool = False
    constant_value: Optional[str] = None


@dataclass
class EventSymbol(BaseSymbol):
    """事件符号"""
    type_info: Optional[TypeInfo] = None
    add_method: Optional[str] = None
    remove_method: Optional[str] = None


@dataclass
class SemanticModel:
    """完整的语义模型"""
    project_path: str
    symbols: Dict[str, BaseSymbol] = field(default_factory=dict)
    namespaces: Dict[str, NamespaceSymbol] = field(default_factory=dict)
    types: Dict[str, TypeSymbol] = field(default_factory=dict)
    methods: Dict[str, MethodSymbol] = field(default_factory=dict)
    properties: Dict[str, PropertySymbol] = field(default_factory=dict)
    fields: Dict[str, FieldSymbol] = field(default_factory=dict)
    events: Dict[str, EventSymbol] = field(default_factory=dict)
    
    # 索引
    symbols_by_file: Dict[str, List[str]] = field(default_factory=dict)
    symbols_by_type: Dict[SymbolKind, List[str]] = field(default_factory=dict)
    inheritance_tree: Dict[str, List[str]] = field(default_factory=dict)  # type_id -> derived_types
    call_graph: Dict[str, List[str]] = field(default_factory=dict)  # method_id -> called_methods
    
    def add_symbol(self, symbol: BaseSymbol):
        """添加符号到模型"""
        self.symbols[symbol.id] = symbol
        
        # 添加到类型特定的字典
        if isinstance(symbol, NamespaceSymbol):
            self.namespaces[symbol.id] = symbol
        elif isinstance(symbol, TypeSymbol):
            self.types[symbol.id] = symbol
        elif isinstance(symbol, MethodSymbol):
            self.methods[symbol.id] = symbol
        elif isinstance(symbol, PropertySymbol):
            self.properties[symbol.id] = symbol
        elif isinstance(symbol, FieldSymbol):
            self.fields[symbol.id] = symbol
        elif isinstance(symbol, EventSymbol):
            self.events[symbol.id] = symbol
        
        # 更新索引
        file_path = symbol.location.file_path
        if file_path not in self.symbols_by_file:
            self.symbols_by_file[file_path] = []
        self.symbols_by_file[file_path].append(symbol.id)
        
        if symbol.kind not in self.symbols_by_type:
            self.symbols_by_type[symbol.kind] = []
        self.symbols_by_type[symbol.kind].append(symbol.id)
    
    def get_symbol(self, symbol_id: str) -> Optional[BaseSymbol]:
        """获取符号"""
        return self.symbols.get(symbol_id)
    
    def get_symbols_in_file(self, file_path: str) -> List[BaseSymbol]:
        """获取文件中的所有符号"""
        symbol_ids = self.symbols_by_file.get(file_path, [])
        return [self.symbols[sid] for sid in symbol_ids if sid in self.symbols]
    
    def get_symbols_by_kind(self, kind: SymbolKind) -> List[BaseSymbol]:
        """按类型获取符号"""
        symbol_ids = self.symbols_by_type.get(kind, [])
        return [self.symbols[sid] for sid in symbol_ids if sid in self.symbols]
    
    def get_derived_types(self, type_id: str) -> List[TypeSymbol]:
        """获取派生类型"""
        derived_ids = self.inheritance_tree.get(type_id, [])
        return [self.types[tid] for tid in derived_ids if tid in self.types]
    
    def get_called_methods(self, method_id: str) -> List[MethodSymbol]:
        """获取方法调用的其他方法"""
        called_ids = self.call_graph.get(method_id, [])
        return [self.methods[mid] for mid in called_ids if mid in self.methods]


@dataclass
class AnalysisResult:
    """分析结果"""
    semantic_model: SemanticModel
    diagnostics: List[Dict[str, Any]] = field(default_factory=list)
    analysis_time: float = 0.0
    files_analyzed: int = 0
    symbols_found: int = 0
    errors: List[str] = field(default_factory=list)
