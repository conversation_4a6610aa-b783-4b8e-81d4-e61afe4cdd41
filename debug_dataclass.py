#!/usr/bin/env python3
"""
调试 dataclass 问题
"""

import sys
from pathlib import Path
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Set, Any
from enum import Enum

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 重新定义所有相关的类来测试
class SymbolKind(Enum):
    """符号类型枚举"""
    NAMESPACE = "namespace"
    CLASS = "class"
    INTERFACE = "interface"
    STRUCT = "struct"
    ENUM = "enum"
    METHOD = "method"
    PROPERTY = "property"
    FIELD = "field"
    EVENT = "event"
    CONSTRUCTOR = "constructor"


class AccessModifier(Enum):
    """访问修饰符枚举"""
    PUBLIC = "public"
    PRIVATE = "private"
    PROTECTED = "protected"
    INTERNAL = "internal"


@dataclass
class Location:
    """代码位置信息"""
    file_path: str = ""
    start_line: int = 0
    start_column: int = 0
    end_line: int = 0
    end_column: int = 0


@dataclass
class TypeInfo:
    """类型信息"""
    name: str = ""
    full_name: str = ""
    namespace: str = ""
    is_generic: bool = False
    generic_parameters: List[str] = field(default_factory=list)
    is_nullable: bool = False
    is_array: bool = False
    array_rank: int = 0


@dataclass
class Parameter:
    """参数信息"""
    name: str = ""
    type_info: Optional[TypeInfo] = None
    is_optional: bool = False
    default_value: Optional[str] = None
    is_params: bool = False
    is_ref: bool = False
    is_out: bool = False
    is_in: bool = False


@dataclass
class SymbolReference:
    """符号引用信息"""
    symbol_id: str = ""
    location: Optional[Location] = None
    reference_type: str = "reference"  # "definition", "reference", "write", "read"


@dataclass
class BaseSymbol:
    """基础符号信息"""
    id: str = ""
    name: str = ""
    full_name: str = ""
    kind: SymbolKind = SymbolKind.CLASS
    access_modifier: AccessModifier = AccessModifier.PUBLIC
    location: Optional[Location] = None
    documentation: Optional[str] = None
    attributes: List[str] = field(default_factory=list)
    is_static: bool = False
    is_abstract: bool = False
    is_virtual: bool = False
    is_override: bool = False
    is_sealed: bool = False
    references: List[SymbolReference] = field(default_factory=list)


@dataclass
class NamespaceSymbol(BaseSymbol):
    """命名空间符号"""
    child_namespaces: List[str] = field(default_factory=list)
    types: List[str] = field(default_factory=list)


@dataclass
class TypeSymbol(BaseSymbol):
    """类型符号（类、接口、结构体、枚举等）"""
    namespace: str = ""
    base_types: List[str] = field(default_factory=list)
    implemented_interfaces: List[str] = field(default_factory=list)
    generic_parameters: List[str] = field(default_factory=list)
    members: List[str] = field(default_factory=list)
    nested_types: List[str] = field(default_factory=list)
    is_partial: bool = False
    is_generic: bool = False


@dataclass
class MethodSymbol(BaseSymbol):
    """方法符号"""
    return_type: Optional[TypeInfo] = None
    parameters: List[Parameter] = field(default_factory=list)
    generic_parameters: List[str] = field(default_factory=list)
    is_async: bool = False
    is_extension: bool = False
    is_partial: bool = False
    is_extern: bool = False
    calls: List[str] = field(default_factory=list)
    called_by: List[str] = field(default_factory=list)


@dataclass
class PropertySymbol(BaseSymbol):
    """属性符号"""
    type_info: Optional[TypeInfo] = None
    has_getter: bool = True
    has_setter: bool = True
    is_auto_property: bool = False
    getter_access: Optional[AccessModifier] = None
    setter_access: Optional[AccessModifier] = None


@dataclass
class FieldSymbol(BaseSymbol):
    """字段符号"""
    type_info: Optional[TypeInfo] = None
    is_readonly: bool = False
    is_const: bool = False
    is_volatile: bool = False
    constant_value: Optional[str] = None


@dataclass
class EventSymbol(BaseSymbol):
    """事件符号"""
    type_info: Optional[TypeInfo] = None
    add_method: Optional[str] = None
    remove_method: Optional[str] = None


def test_dataclasses():
    """测试 dataclass 定义"""
    try:
        print("1. 测试 Location...")
        location = Location()
        print("✅ Location 创建成功")
        
        print("2. 测试 TypeInfo...")
        type_info = TypeInfo()
        print("✅ TypeInfo 创建成功")
        
        print("3. 测试 Parameter...")
        parameter = Parameter()
        print("✅ Parameter 创建成功")
        
        print("4. 测试 SymbolReference...")
        ref = SymbolReference()
        print("✅ SymbolReference 创建成功")
        
        print("5. 测试 BaseSymbol...")
        base_symbol = BaseSymbol()
        print("✅ BaseSymbol 创建成功")
        
        print("6. 测试 NamespaceSymbol...")
        namespace_symbol = NamespaceSymbol()
        print("✅ NamespaceSymbol 创建成功")
        
        print("7. 测试 TypeSymbol...")
        type_symbol = TypeSymbol()
        print("✅ TypeSymbol 创建成功")
        
        print("8. 测试 MethodSymbol...")
        method_symbol = MethodSymbol()
        print("✅ MethodSymbol 创建成功")
        
        print("9. 测试 PropertySymbol...")
        property_symbol = PropertySymbol()
        print("✅ PropertySymbol 创建成功")
        
        print("10. 测试 FieldSymbol...")
        field_symbol = FieldSymbol()
        print("✅ FieldSymbol 创建成功")
        
        print("11. 测试 EventSymbol...")
        event_symbol = EventSymbol()
        print("✅ EventSymbol 创建成功")
        
        print("\n🎉 所有 dataclass 测试通过！")
        
    except Exception as e:
        print(f"❌ dataclass 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_dataclasses()
