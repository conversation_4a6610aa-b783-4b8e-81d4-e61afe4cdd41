#!/usr/bin/env python3
"""
语义分析演示脚本

演示如何使用增强的 OmniSharp 语义分析器获取完整的代码语义模型
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.csharp_assistant.interfaces.language_server.omnisharp_client import OmniSharpClient
from src.csharp_assistant.core.services.semantic_analyzer import SemanticAnalyzer
from src.csharp_assistant.config.config import get_config


async def demo_semantic_analysis():
    """演示语义分析功能"""
    print("🎯 OmniSharp 语义分析演示")
    print("=" * 50)
    
    try:
        # 1. 初始化配置和客户端
        print("📋 1. 初始化配置...")
        config = get_config()
        print(f"   项目路径: {config.project_path}")
        print(f"   OmniSharp 路径: {config.language_server_path}")
        
        # 2. 创建 OmniSharp 客户端
        print("\n🔧 2. 创建 OmniSharp 客户端...")
        omnisharp_client = OmniSharpClient()
        
        # 3. 创建语义分析器
        print("🧠 3. 创建语义分析器...")
        analyzer = SemanticAnalyzer(omnisharp_client, config)
        
        # 4. 启动 OmniSharp
        print("\n🚀 4. 启动 OmniSharp 服务...")
        await omnisharp_client.start(config.project_path)
        
        if not omnisharp_client.is_initialized:
            print("❌ OmniSharp 启动失败，请检查配置")
            return
        
        print("✅ OmniSharp 启动成功")
        
        # 5. 执行语义分析
        print(f"\n📊 5. 开始分析项目...")
        print("   这可能需要几分钟时间，请耐心等待...")
        
        result = await analyzer.analyze_project(config.project_path)
        
        # 6. 展示分析结果
        print(f"\n📈 6. 分析结果:")
        print(f"   ⏱️  分析耗时: {result.analysis_time:.2f} 秒")
        print(f"   📁 分析文件: {result.files_analyzed} 个")
        print(f"   🔍 发现符号: {result.symbols_found} 个")
        
        if result.errors:
            print(f"   ⚠️  错误数量: {len(result.errors)}")
        
        # 7. 展示语义模型详情
        print(f"\n🏗️ 7. 语义模型详情:")
        semantic_model = result.semantic_model
        
        print(f"   📦 命名空间: {len(semantic_model.namespaces)}")
        print(f"   🏛️  类型: {len(semantic_model.types)}")
        print(f"   🔧 方法: {len(semantic_model.methods)}")
        print(f"   🏷️  属性: {len(semantic_model.properties)}")
        print(f"   📋 字段: {len(semantic_model.fields)}")
        print(f"   📡 事件: {len(semantic_model.events)}")
        
        # 8. 展示继承关系
        if semantic_model.inheritance_tree:
            print(f"\n🌳 8. 继承关系:")
            print(f"   发现 {len(semantic_model.inheritance_tree)} 个基类型")
            
            # 显示一些继承关系示例
            count = 0
            for base_id, derived_ids in semantic_model.inheritance_tree.items():
                if count >= 3:  # 只显示前3个
                    break
                base_symbol = semantic_model.get_symbol(base_id)
                if base_symbol:
                    print(f"   📦 {base_symbol.name}")
                    for derived_id in derived_ids[:2]:  # 每个基类只显示前2个派生类
                        derived_symbol = semantic_model.get_symbol(derived_id)
                        if derived_symbol:
                            print(f"      └── {derived_symbol.name}")
                count += 1
        
        # 9. 展示调用关系
        if semantic_model.call_graph:
            print(f"\n📞 9. 调用关系:")
            print(f"   发现 {len(semantic_model.call_graph)} 个方法调用关系")
        
        # 10. 展示文件分布
        print(f"\n📁 10. 文件符号分布:")
        file_count = 0
        for file_path, symbol_ids in semantic_model.symbols_by_file.items():
            if file_count >= 5:  # 只显示前5个文件
                break
            file_name = Path(file_path).name
            print(f"    📄 {file_name}: {len(symbol_ids)} 个符号")
            file_count += 1
        
        if len(semantic_model.symbols_by_file) > 5:
            print(f"    ... 还有 {len(semantic_model.symbols_by_file) - 5} 个文件")
        
        # 11. 展示一些具体的符号示例
        print(f"\n🔍 11. 符号示例:")
        
        # 显示类示例
        if semantic_model.types:
            print("   📦 类示例:")
            for type_symbol in list(semantic_model.types.values())[:3]:
                print(f"      🏛️  {type_symbol.full_name}")
                print(f"         📍 位置: {Path(type_symbol.location.file_path).name}:{type_symbol.location.start_line}")
                if hasattr(type_symbol, 'base_types') and type_symbol.base_types:
                    print(f"         🔗 继承: {', '.join(type_symbol.base_types)}")
        
        # 显示方法示例
        if semantic_model.methods:
            print("   🔧 方法示例:")
            for method_symbol in list(semantic_model.methods.values())[:3]:
                print(f"      ⚙️  {method_symbol.full_name}")
                print(f"         📍 位置: {Path(method_symbol.location.file_path).name}:{method_symbol.location.start_line}")
                if hasattr(method_symbol, 'return_type'):
                    print(f"         🔄 返回: {method_symbol.return_type.name}")
        
        # 12. 清理资源
        print(f"\n🧹 12. 清理资源...")
        await omnisharp_client.stop()
        
        print("\n✅ 演示完成！")
        print("\n💡 提示:")
        print("   - 语义分析器已成功解析了项目的完整语义模型")
        print("   - 包含了类、方法、属性、字段等所有符号信息")
        print("   - 分析了继承关系和调用关系")
        print("   - 可以用于代码理解、重构、依赖分析等场景")
        
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


async def demo_specific_analysis():
    """演示特定分析功能"""
    print("\n" + "=" * 50)
    print("🎯 特定分析功能演示")
    print("=" * 50)
    
    try:
        config = get_config()
        omnisharp_client = OmniSharpClient()
        analyzer = SemanticAnalyzer(omnisharp_client, config)
        
        await omnisharp_client.start(config.project_path)
        
        if not omnisharp_client.is_initialized:
            print("❌ OmniSharp 未启动，跳过特定分析")
            return
        
        # 分析项目
        result = await analyzer.analyze_project(config.project_path)
        semantic_model = result.semantic_model
        
        # 1. 查找特定类型的符号
        print("🔍 1. 查找 Godot 相关的类:")
        godot_classes = []
        for type_symbol in semantic_model.types.values():
            if any(base in type_symbol.base_types for base in ["Node", "Node2D", "Node3D", "Control", "Resource"]):
                godot_classes.append(type_symbol)
        
        if godot_classes:
            print(f"   找到 {len(godot_classes)} 个 Godot 类:")
            for cls in godot_classes[:5]:
                print(f"      🎮 {cls.name} (继承: {', '.join(cls.base_types)})")
        else:
            print("   未找到明显的 Godot 类")
        
        # 2. 分析方法复杂度
        print(f"\n📊 2. 方法复杂度分析:")
        if semantic_model.methods:
            method_with_params = [m for m in semantic_model.methods.values() if hasattr(m, 'parameters') and m.parameters]
            print(f"   有参数的方法: {len(method_with_params)}")
            
            if method_with_params:
                max_params = max(len(m.parameters) for m in method_with_params)
                complex_methods = [m for m in method_with_params if len(m.parameters) >= max_params]
                print(f"   最复杂方法 (参数数: {max_params}):")
                for method in complex_methods[:3]:
                    param_types = [p.type_info.name for p in method.parameters]
                    print(f"      🔧 {method.name}({', '.join(param_types)})")
        
        # 3. 分析命名空间分布
        print(f"\n📦 3. 命名空间分布:")
        namespace_counts = {}
        for symbol in semantic_model.symbols.values():
            if hasattr(symbol, 'namespace') and symbol.namespace:
                namespace_counts[symbol.namespace] = namespace_counts.get(symbol.namespace, 0) + 1
        
        if namespace_counts:
            sorted_namespaces = sorted(namespace_counts.items(), key=lambda x: x[1], reverse=True)
            for namespace, count in sorted_namespaces[:5]:
                print(f"   📁 {namespace}: {count} 个符号")
        
        await omnisharp_client.stop()
        
        print("\n✅ 特定分析完成！")
        
    except Exception as e:
        print(f"❌ 特定分析失败: {e}")


async def main():
    """主函数"""
    await demo_semantic_analysis()
    await demo_specific_analysis()


if __name__ == "__main__":
    asyncio.run(main())
