"""
增强的 OmniSharp 语义分析器

使用 OmniSharp 获取完整的语义模型，包括类、方法、变量、引用关系、继承链等
"""

import asyncio
import json
import logging
import time
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Any, Set

from ..domain.semantic_model import (
    SemanticModel, AnalysisResult, BaseSymbol, TypeSymbol, MethodSymbol,
    PropertySymbol, FieldSymbol, NamespaceSymbol, EventSymbol,
    SymbolKind, AccessModifier, Location, TypeInfo, Parameter, SymbolReference
)
from ...interfaces.language_server.omnisharp_http_client import OmniSharpHttpClient

logger = logging.getLogger(__name__)


class SemanticAnalyzer:
    """语义分析器"""

    def __init__(self, omnisharp_client, config=None):
        """
        初始化语义分析器

        Args:
            omnisharp_client: OmniSharp LSP 客户端实例
            config: 配置对象
        """
        self.omnisharp_client = omnisharp_client
        self.http_client = OmniSharpHttpClient(config)
        self.semantic_model = None
        self._request_id = 0
        self.use_http_api = True  # 优先使用 HTTP API
    
    def _get_next_request_id(self) -> int:
        """获取下一个请求ID"""
        self._request_id += 1
        return self._request_id
    
    async def analyze_project(self, project_path: str) -> AnalysisResult:
        """
        分析整个项目

        Args:
            project_path: 项目路径

        Returns:
            AnalysisResult: 分析结果
        """
        start_time = time.time()
        logger.info(f"开始分析项目: {project_path}")

        try:
            # 初始化语义模型
            self.semantic_model = SemanticModel(project_path=project_path)

            # 尝试启动 HTTP API
            http_started = False
            if self.use_http_api:
                try:
                    http_started = await self.http_client.start(project_path)
                    if http_started:
                        logger.info("使用 OmniSharp HTTP API 进行分析")
                    else:
                        logger.warning("HTTP API 启动失败，回退到 LSP 模式")
                except Exception as e:
                    logger.warning(f"HTTP API 启动异常: {e}，回退到 LSP 模式")

            # 如果 HTTP API 不可用，确保 LSP 客户端已初始化
            if not http_started and not self.omnisharp_client.is_initialized:
                logger.error("OmniSharp 未初始化，无法进行语义分析")
                return AnalysisResult(
                    semantic_model=self.semantic_model,
                    errors=["OmniSharp 未初始化"]
                )

            # 1. 获取项目级别的符号信息
            if http_started:
                await self._analyze_code_structure_http()
            else:
                await self._analyze_workspace_symbols()

            # 2. 分析每个文件的详细符号信息
            cs_files = list(Path(project_path).rglob("*.cs"))
            logger.info(f"找到 {len(cs_files)} 个 C# 文件")

            for file_path in cs_files:
                try:
                    await self._analyze_file_symbols(str(file_path))
                except Exception as e:
                    logger.error(f"分析文件 {file_path} 失败: {e}")
                    self.semantic_model.errors.append(f"分析文件 {file_path} 失败: {e}")

            # 3. 分析引用关系和调用图
            await self._analyze_references()

            # 4. 构建继承树
            self._build_inheritance_tree()

            # 5. 构建调用图
            self._build_call_graph()

            # 清理资源
            if http_started:
                await self.http_client.stop()

            analysis_time = time.time() - start_time

            result = AnalysisResult(
                semantic_model=self.semantic_model,
                analysis_time=analysis_time,
                files_analyzed=len(cs_files),
                symbols_found=len(self.semantic_model.symbols)
            )

            logger.info(f"项目分析完成，耗时 {analysis_time:.2f}s，找到 {result.symbols_found} 个符号")
            return result

        except Exception as e:
            logger.error(f"项目分析失败: {e}", exc_info=True)
            return AnalysisResult(
                semantic_model=self.semantic_model or SemanticModel(project_path=project_path),
                errors=[f"项目分析失败: {e}"]
            )
    
    async def _analyze_code_structure_http(self):
        """使用 HTTP API 分析代码结构"""
        logger.info("使用 HTTP API 获取代码结构...")

        try:
            # 获取代码结构
            structure_data = await self.http_client.get_code_structure()

            if structure_data and "Elements" in structure_data:
                elements = structure_data["Elements"]
                logger.info(f"获取到 {len(elements)} 个代码结构元素")

                for element in elements:
                    await self._process_code_structure_element(element)
            else:
                logger.warning("HTTP API 代码结构请求返回空结果")

        except Exception as e:
            logger.error(f"获取代码结构失败: {e}")

    async def _process_code_structure_element(self, element: Dict[str, Any]):
        """处理代码结构元素"""
        try:
            kind = element.get("Kind", "")
            name = element.get("Name", "")
            display_name = element.get("DisplayName", name)

            # 解析位置信息
            ranges = element.get("Ranges", {})
            full_range = ranges.get("full", {})

            if not full_range:
                return

            location = Location(
                file_path=full_range.get("FileName", ""),
                start_line=full_range.get("Start", {}).get("Line", 0) + 1,
                start_column=full_range.get("Start", {}).get("Column", 0) + 1,
                end_line=full_range.get("End", {}).get("Line", 0) + 1,
                end_column=full_range.get("End", {}).get("Column", 0) + 1
            )

            # 转换符号类型
            symbol_kind = self._convert_omnisharp_kind(kind)
            if not symbol_kind:
                return

            symbol_id = str(uuid.uuid4())

            # 根据类型创建相应的符号
            if symbol_kind == SymbolKind.CLASS:
                type_symbol = TypeSymbol(
                    id=symbol_id,
                    name=name,
                    full_name=display_name,
                    kind=symbol_kind,
                    access_modifier=self._parse_access_modifier_from_properties(element),
                    location=location,
                    namespace=self._extract_namespace_from_display_name(display_name)
                )
                self.semantic_model.add_symbol(type_symbol)

            elif symbol_kind == SymbolKind.METHOD:
                method_symbol = MethodSymbol(
                    id=symbol_id,
                    name=name,
                    full_name=display_name,
                    kind=symbol_kind,
                    access_modifier=self._parse_access_modifier_from_properties(element),
                    location=location,
                    return_type=TypeInfo(name="void", full_name="void", namespace="")  # 简化处理
                )
                self.semantic_model.add_symbol(method_symbol)

            # 递归处理子元素
            children = element.get("Children", [])
            for child in children:
                await self._process_code_structure_element(child)

        except Exception as e:
            logger.error(f"处理代码结构元素失败: {e}")

    def _convert_omnisharp_kind(self, kind: str) -> Optional[SymbolKind]:
        """转换 OmniSharp 符号类型"""
        mapping = {
            "namespace": SymbolKind.NAMESPACE,
            "class": SymbolKind.CLASS,
            "interface": SymbolKind.INTERFACE,
            "struct": SymbolKind.STRUCT,
            "enum": SymbolKind.ENUM,
            "method": SymbolKind.METHOD,
            "constructor": SymbolKind.CONSTRUCTOR,
            "property": SymbolKind.PROPERTY,
            "field": SymbolKind.FIELD,
            "event": SymbolKind.EVENT
        }
        return mapping.get(kind.lower())

    def _parse_access_modifier_from_properties(self, element: Dict[str, Any]) -> AccessModifier:
        """从元素属性中解析访问修饰符"""
        properties = element.get("Properties", {})
        accessibility = properties.get("accessibility", "public")
        return self._parse_access_modifier(accessibility)

    def _extract_namespace_from_display_name(self, display_name: str) -> str:
        """从显示名称中提取命名空间"""
        parts = display_name.split(".")
        if len(parts) > 1:
            return ".".join(parts[:-1])
        return ""

    async def _analyze_workspace_symbols(self):
        """分析工作区符号"""
        logger.info("获取工作区符号...")

        try:
            # 发送 workspace/symbol 请求
            request = {
                "jsonrpc": "2.0",
                "id": self._get_next_request_id(),
                "method": "workspace/symbol",
                "params": {
                    "query": ""  # 空查询获取所有符号
                }
            }

            response = await self.omnisharp_client._send_request(request)

            if response and "result" in response:
                symbols = response["result"]
                logger.info(f"获取到 {len(symbols)} 个工作区符号")

                for symbol_data in symbols:
                    await self._process_workspace_symbol(symbol_data)
            else:
                logger.warning("workspace/symbol 请求返回空结果")

        except Exception as e:
            logger.error(f"获取工作区符号失败: {e}")
    
    async def _process_workspace_symbol(self, symbol_data: Dict[str, Any]):
        """处理工作区符号"""
        try:
            name = symbol_data.get("name", "")
            kind = symbol_data.get("kind", 0)
            location_data = symbol_data.get("location", {})
            
            # 解析位置信息
            location = self._parse_location(location_data)
            if not location:
                return
            
            # 转换符号类型
            symbol_kind = self._convert_lsp_symbol_kind(kind)
            if not symbol_kind:
                return
            
            # 生成符号ID
            symbol_id = str(uuid.uuid4())
            
            # 创建基础符号信息
            symbol = BaseSymbol(
                id=symbol_id,
                name=name,
                full_name=symbol_data.get("containerName", "") + "." + name if symbol_data.get("containerName") else name,
                kind=symbol_kind,
                access_modifier=AccessModifier.PUBLIC,  # 默认值，后续会更新
                location=location
            )
            
            self.semantic_model.add_symbol(symbol)
            
        except Exception as e:
            logger.error(f"处理工作区符号失败: {e}")
    
    async def _analyze_file_symbols(self, file_path: str):
        """分析文件符号"""
        logger.debug(f"分析文件符号: {file_path}")
        
        try:
            # 使用现有的 analyze_code 方法
            analysis_result = await self.omnisharp_client.analyze_code(file_path)
            
            if not analysis_result:
                logger.warning(f"文件 {file_path} 分析返回空结果")
                return
            
            # 处理类
            for class_data in analysis_result.get("classes", []):
                await self._process_class_symbol(class_data, file_path)
            
            # 处理方法
            for method_data in analysis_result.get("methods", []):
                await self._process_method_symbol(method_data, file_path)
            
            # 处理字段
            for field_data in analysis_result.get("fields", []):
                await self._process_field_symbol(field_data, file_path)
                
        except Exception as e:
            logger.error(f"分析文件符号失败: {e}")
    
    async def _process_class_symbol(self, class_data: Dict[str, Any], file_path: str):
        """处理类符号"""
        try:
            symbol_id = str(uuid.uuid4())
            name = class_data.get("name", "")
            full_name = class_data.get("full_name", name)
            
            # 创建位置信息（简化版本，实际需要从LSP获取）
            location = Location(
                file_path=file_path,
                start_line=1,
                start_column=1,
                end_line=1,
                end_column=1
            )
            
            # 解析访问修饰符
            access_modifier = self._parse_access_modifier(class_data.get("access_modifier", "public"))
            
            type_symbol = TypeSymbol(
                id=symbol_id,
                name=name,
                full_name=full_name,
                kind=SymbolKind.CLASS,
                access_modifier=access_modifier,
                location=location,
                namespace=class_data.get("namespace", ""),
                base_types=class_data.get("base_types", []),
                is_static=class_data.get("is_static", False),
                is_abstract=class_data.get("is_abstract", False),
                is_sealed=class_data.get("is_sealed", False)
            )
            
            self.semantic_model.add_symbol(type_symbol)
            
        except Exception as e:
            logger.error(f"处理类符号失败: {e}")
    
    async def _process_method_symbol(self, method_data: Dict[str, Any], file_path: str):
        """处理方法符号"""
        try:
            symbol_id = str(uuid.uuid4())
            name = method_data.get("name", "")
            full_name = method_data.get("full_name", name)
            
            location = Location(
                file_path=file_path,
                start_line=1,
                start_column=1,
                end_line=1,
                end_column=1
            )
            
            access_modifier = self._parse_access_modifier(method_data.get("access_modifier", "public"))
            
            # 解析返回类型
            return_type = TypeInfo(
                name=method_data.get("return_type", "void"),
                full_name=method_data.get("return_type", "void"),
                namespace=""
            )
            
            # 解析参数
            parameters = []
            for param_data in method_data.get("parameters", []):
                param_type = TypeInfo(
                    name=param_data.get("type", "object"),
                    full_name=param_data.get("type", "object"),
                    namespace=""
                )
                parameter = Parameter(
                    name=param_data.get("name", ""),
                    type_info=param_type
                )
                parameters.append(parameter)
            
            method_symbol = MethodSymbol(
                id=symbol_id,
                name=name,
                full_name=full_name,
                kind=SymbolKind.METHOD,
                access_modifier=access_modifier,
                location=location,
                return_type=return_type,
                parameters=parameters,
                is_static=method_data.get("is_static", False),
                is_async=method_data.get("is_async", False),
                is_virtual=method_data.get("is_virtual", False),
                is_override=method_data.get("is_override", False),
                is_abstract=method_data.get("is_abstract", False)
            )
            
            self.semantic_model.add_symbol(method_symbol)
            
        except Exception as e:
            logger.error(f"处理方法符号失败: {e}")
    
    async def _process_field_symbol(self, field_data: Dict[str, Any], file_path: str):
        """处理字段符号"""
        try:
            symbol_id = str(uuid.uuid4())
            name = field_data.get("name", "")
            full_name = field_data.get("full_name", name)
            
            location = Location(
                file_path=file_path,
                start_line=1,
                start_column=1,
                end_line=1,
                end_column=1
            )
            
            access_modifier = self._parse_access_modifier(field_data.get("access_modifier", "private"))
            
            # 解析字段类型
            field_type = TypeInfo(
                name=field_data.get("type", "object"),
                full_name=field_data.get("type", "object"),
                namespace=""
            )
            
            # 判断是属性还是字段
            if field_data.get("has_getter") or field_data.get("has_setter"):
                # 属性
                property_symbol = PropertySymbol(
                    id=symbol_id,
                    name=name,
                    full_name=full_name,
                    kind=SymbolKind.PROPERTY,
                    access_modifier=access_modifier,
                    location=location,
                    type_info=field_type,
                    has_getter=field_data.get("has_getter", True),
                    has_setter=field_data.get("has_setter", True),
                    is_auto_property=field_data.get("is_auto_property", False),
                    is_static=field_data.get("is_static", False)
                )
                self.semantic_model.add_symbol(property_symbol)
            else:
                # 字段
                field_symbol = FieldSymbol(
                    id=symbol_id,
                    name=name,
                    full_name=full_name,
                    kind=SymbolKind.FIELD,
                    access_modifier=access_modifier,
                    location=location,
                    type_info=field_type,
                    is_static=field_data.get("is_static", False)
                )
                self.semantic_model.add_symbol(field_symbol)
            
        except Exception as e:
            logger.error(f"处理字段符号失败: {e}")
    
    def _parse_location(self, location_data: Dict[str, Any]) -> Optional[Location]:
        """解析位置信息"""
        try:
            uri = location_data.get("uri", "")
            if not uri:
                return None
            
            # 转换 URI 到文件路径
            file_path = uri.replace("file:///", "").replace("/", "\\")
            
            range_data = location_data.get("range", {})
            start = range_data.get("start", {})
            end = range_data.get("end", {})
            
            return Location(
                file_path=file_path,
                start_line=start.get("line", 0) + 1,  # LSP 使用 0 基索引
                start_column=start.get("character", 0) + 1,
                end_line=end.get("line", 0) + 1,
                end_column=end.get("character", 0) + 1
            )
        except Exception as e:
            logger.error(f"解析位置信息失败: {e}")
            return None
    
    def _convert_lsp_symbol_kind(self, kind: int) -> Optional[SymbolKind]:
        """转换 LSP 符号类型"""
        mapping = {
            1: SymbolKind.NAMESPACE,
            5: SymbolKind.CLASS,
            11: SymbolKind.INTERFACE,
            23: SymbolKind.STRUCT,
            10: SymbolKind.ENUM,
            6: SymbolKind.METHOD,
            9: SymbolKind.CONSTRUCTOR,
            7: SymbolKind.PROPERTY,
            8: SymbolKind.FIELD,
            24: SymbolKind.EVENT
        }
        return mapping.get(kind)
    
    def _parse_access_modifier(self, modifier: str) -> AccessModifier:
        """解析访问修饰符"""
        modifier_lower = modifier.lower()
        if "private" in modifier_lower:
            return AccessModifier.PRIVATE
        elif "protected" in modifier_lower:
            return AccessModifier.PROTECTED
        elif "internal" in modifier_lower:
            return AccessModifier.INTERNAL
        else:
            return AccessModifier.PUBLIC
    
    async def _analyze_references(self):
        """分析引用关系"""
        logger.info("分析引用关系...")

        try:
            # 为每个符号分析引用关系
            for symbol in self.semantic_model.symbols.values():
                if symbol.kind in [SymbolKind.CLASS, SymbolKind.METHOD, SymbolKind.PROPERTY, SymbolKind.FIELD]:
                    await self._analyze_symbol_references(symbol)

        except Exception as e:
            logger.error(f"分析引用关系失败: {e}")

    async def _analyze_symbol_references(self, symbol: BaseSymbol):
        """分析单个符号的引用关系"""
        try:
            if self.http_client.is_running:
                # 使用 HTTP API
                usages = await self.http_client.find_usages(
                    symbol.location.file_path,
                    symbol.location.start_line,
                    symbol.location.start_column
                )

                if usages and "QuickFixes" in usages:
                    for usage in usages["QuickFixes"]:
                        location_data = usage.get("Location", {})
                        if location_data:
                            reference = SymbolReference(
                                symbol_id=symbol.id,
                                location=self._parse_location_from_omnisharp(location_data),
                                reference_type="reference"
                            )
                            symbol.references.append(reference)
            else:
                # 使用 LSP API
                await self._analyze_symbol_references_lsp(symbol)

        except Exception as e:
            logger.debug(f"分析符号 {symbol.name} 的引用关系失败: {e}")

    async def _analyze_symbol_references_lsp(self, symbol: BaseSymbol):
        """使用 LSP API 分析符号引用"""
        try:
            # 发送 textDocument/references 请求
            file_uri = self.omnisharp_client._path_to_uri(symbol.location.file_path)

            request = {
                "jsonrpc": "2.0",
                "id": self._get_next_request_id(),
                "method": "textDocument/references",
                "params": {
                    "textDocument": {"uri": file_uri},
                    "position": {
                        "line": symbol.location.start_line - 1,  # LSP 使用 0 基索引
                        "character": symbol.location.start_column - 1
                    },
                    "context": {
                        "includeDeclaration": True
                    }
                }
            }

            response = await self.omnisharp_client._send_request(request)

            if response and "result" in response:
                references = response["result"]
                for ref_data in references:
                    location = self._parse_location({"uri": ref_data.get("uri"), "range": ref_data.get("range")})
                    if location:
                        reference = SymbolReference(
                            symbol_id=symbol.id,
                            location=location,
                            reference_type="reference"
                        )
                        symbol.references.append(reference)

        except Exception as e:
            logger.debug(f"LSP 分析符号 {symbol.name} 的引用关系失败: {e}")

    def _parse_location_from_omnisharp(self, location_data: Dict[str, Any]) -> Location:
        """解析 OmniSharp 位置信息"""
        try:
            return Location(
                file_path=location_data.get("FileName", ""),
                start_line=location_data.get("Line", 0) + 1,
                start_column=location_data.get("Column", 0) + 1,
                end_line=location_data.get("EndLine", location_data.get("Line", 0)) + 1,
                end_column=location_data.get("EndColumn", location_data.get("Column", 0)) + 1
            )
        except Exception as e:
            logger.error(f"解析 OmniSharp 位置信息失败: {e}")
            return Location(file_path="", start_line=0, start_column=0, end_line=0, end_column=0)
    
    def _build_inheritance_tree(self):
        """构建继承树"""
        logger.info("构建继承树...")
        
        for type_symbol in self.semantic_model.types.values():
            for base_type in type_symbol.base_types:
                # 查找基类型
                base_symbol = None
                for ts in self.semantic_model.types.values():
                    if ts.full_name == base_type or ts.name == base_type:
                        base_symbol = ts
                        break
                
                if base_symbol:
                    if base_symbol.id not in self.semantic_model.inheritance_tree:
                        self.semantic_model.inheritance_tree[base_symbol.id] = []
                    self.semantic_model.inheritance_tree[base_symbol.id].append(type_symbol.id)
    
    def _build_call_graph(self):
        """构建调用图"""
        logger.info("构建调用图...")
        
        for method_symbol in self.semantic_model.methods.values():
            # 这里需要分析方法体来找到调用关系
            # 由于复杂性，暂时使用现有的 calls 信息
            if method_symbol.calls:
                self.semantic_model.call_graph[method_symbol.id] = method_symbol.calls
