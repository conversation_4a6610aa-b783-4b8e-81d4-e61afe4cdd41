#!/usr/bin/env python3
"""
测试增强的语义分析器

验证 OmniSharp 语义分析器能够正确解析 Godot C# 项目，获取完整的语义模型
"""

import asyncio
import sys
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.csharp_assistant.interfaces.language_server.omnisharp_client import OmniSharpClient
from src.csharp_assistant.core.services.semantic_analyzer import SemanticAnalyzer
from src.csharp_assistant.config.config import get_config
from src.csharp_assistant.core.domain.semantic_model import SymbolKind


async def test_semantic_analyzer():
    """测试语义分析器"""
    print("🔍 测试增强的语义分析器...")
    
    try:
        config = get_config()
        
        # 初始化 OmniSharp 客户端
        omnisharp_client = OmniSharpClient()
        
        # 创建语义分析器
        analyzer = SemanticAnalyzer(omnisharp_client, config)
        
        # 启动 OmniSharp
        print("🚀 启动 OmniSharp...")
        await omnisharp_client.start(config.project_path)
        
        if not omnisharp_client.is_initialized:
            print("❌ OmniSharp 启动失败")
            return
        
        print("✅ OmniSharp 启动成功")
        
        # 分析项目
        print(f"📊 开始分析项目: {config.project_path}")
        start_time = time.time()
        
        result = await analyzer.analyze_project(config.project_path)
        
        analysis_time = time.time() - start_time
        
        # 输出分析结果
        print(f"\n📈 分析完成，耗时: {analysis_time:.2f}s")
        print(f"📁 分析文件数: {result.files_analyzed}")
        print(f"🔍 发现符号数: {result.symbols_found}")
        
        if result.errors:
            print(f"⚠️ 错误数: {len(result.errors)}")
            for error in result.errors[:5]:  # 只显示前5个错误
                print(f"   - {error}")
        
        # 统计各类型符号
        semantic_model = result.semantic_model
        
        print("\n📊 符号统计:")
        for kind in SymbolKind:
            symbols = semantic_model.get_symbols_by_kind(kind)
            if symbols:
                print(f"   {kind.value}: {len(symbols)}")
        
        # 显示一些示例符号
        print("\n🔍 示例符号:")
        
        # 显示类
        classes = semantic_model.get_symbols_by_kind(SymbolKind.CLASS)
        if classes:
            print(f"\n📦 类 (前5个):")
            for cls in classes[:5]:
                print(f"   - {cls.full_name} ({cls.location.file_path}:{cls.location.start_line})")
                if hasattr(cls, 'base_types') and cls.base_types:
                    print(f"     继承: {', '.join(cls.base_types)}")
        
        # 显示方法
        methods = semantic_model.get_symbols_by_kind(SymbolKind.METHOD)
        if methods:
            print(f"\n🔧 方法 (前5个):")
            for method in methods[:5]:
                print(f"   - {method.full_name} ({method.location.file_path}:{method.location.start_line})")
                if hasattr(method, 'return_type'):
                    print(f"     返回类型: {method.return_type.name}")
                if hasattr(method, 'parameters'):
                    param_names = [p.name for p in method.parameters]
                    print(f"     参数: {', '.join(param_names) if param_names else '无'}")
        
        # 显示属性
        properties = semantic_model.get_symbols_by_kind(SymbolKind.PROPERTY)
        if properties:
            print(f"\n🏷️ 属性 (前5个):")
            for prop in properties[:5]:
                print(f"   - {prop.full_name} ({prop.location.file_path}:{prop.location.start_line})")
                if hasattr(prop, 'type_info'):
                    print(f"     类型: {prop.type_info.name}")
        
        # 显示字段
        fields = semantic_model.get_symbols_by_kind(SymbolKind.FIELD)
        if fields:
            print(f"\n📋 字段 (前5个):")
            for field in fields[:5]:
                print(f"   - {field.full_name} ({field.location.file_path}:{field.location.start_line})")
                if hasattr(field, 'type_info'):
                    print(f"     类型: {field.type_info.name}")
        
        # 显示继承关系
        if semantic_model.inheritance_tree:
            print(f"\n🌳 继承关系 (前5个):")
            count = 0
            for base_id, derived_ids in semantic_model.inheritance_tree.items():
                if count >= 5:
                    break
                base_symbol = semantic_model.get_symbol(base_id)
                if base_symbol:
                    print(f"   {base_symbol.name} -> {len(derived_ids)} 个派生类")
                    for derived_id in derived_ids[:3]:  # 只显示前3个派生类
                        derived_symbol = semantic_model.get_symbol(derived_id)
                        if derived_symbol:
                            print(f"     - {derived_symbol.name}")
                count += 1
        
        # 显示调用关系
        if semantic_model.call_graph:
            print(f"\n📞 调用关系 (前5个):")
            count = 0
            for caller_id, called_ids in semantic_model.call_graph.items():
                if count >= 5:
                    break
                caller_symbol = semantic_model.get_symbol(caller_id)
                if caller_symbol:
                    print(f"   {caller_symbol.name} -> 调用 {len(called_ids)} 个方法")
                count += 1
        
        # 显示引用关系统计
        total_references = sum(len(symbol.references) for symbol in semantic_model.symbols.values())
        if total_references > 0:
            print(f"\n🔗 引用关系: 总共 {total_references} 个引用")
        
        # 按文件显示符号分布
        print(f"\n📁 文件符号分布 (前5个文件):")
        count = 0
        for file_path, symbol_ids in semantic_model.symbols_by_file.items():
            if count >= 5:
                break
            print(f"   {Path(file_path).name}: {len(symbol_ids)} 个符号")
            count += 1
        
        # 停止 OmniSharp
        await omnisharp_client.stop()
        
        print("\n✅ 语义分析器测试完成")
        
        # 保存分析结果到文件
        output_file = "semantic_analysis_result.json"
        await save_analysis_result(result, output_file)
        print(f"📄 分析结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def save_analysis_result(result, output_file: str):
    """保存分析结果到文件"""
    try:
        # 将分析结果转换为可序列化的格式
        data = {
            "analysis_time": result.analysis_time,
            "files_analyzed": result.files_analyzed,
            "symbols_found": result.symbols_found,
            "errors": result.errors,
            "symbols": {},
            "statistics": {}
        }
        
        # 统计各类型符号数量
        for kind in SymbolKind:
            symbols = result.semantic_model.get_symbols_by_kind(kind)
            data["statistics"][kind.value] = len(symbols)
        
        # 保存部分符号信息（避免文件过大）
        for symbol_id, symbol in list(result.semantic_model.symbols.items())[:100]:  # 只保存前100个
            data["symbols"][symbol_id] = {
                "name": symbol.name,
                "full_name": symbol.full_name,
                "kind": symbol.kind.value,
                "access_modifier": symbol.access_modifier.value,
                "file_path": symbol.location.file_path,
                "line": symbol.location.start_line,
                "references_count": len(symbol.references)
            }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
            
    except Exception as e:
        print(f"保存分析结果失败: {e}")


async def main():
    """主函数"""
    await test_semantic_analyzer()


if __name__ == "__main__":
    asyncio.run(main())
