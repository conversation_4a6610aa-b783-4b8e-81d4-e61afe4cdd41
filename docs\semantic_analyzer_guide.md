# OmniSharp 语义分析器使用指南

## 概述

增强的 OmniSharp 语义分析器能够解析 C# 代码，获取完整的语义模型，包括：

- **类、接口、结构体、枚举** 等类型信息
- **方法、属性、字段、事件** 等成员信息  
- **继承关系** 和 **调用关系**
- **引用关系** 和 **符号位置**
- **命名空间** 和 **访问修饰符**

## 核心组件

### 1. 语义模型 (SemanticModel)

完整的语义模型包含：

```python
from src.csharp_assistant.core.domain.semantic_model import SemanticModel

# 语义模型包含的信息
semantic_model = SemanticModel(project_path="path/to/project")
- symbols: Dict[str, BaseSymbol]           # 所有符号
- types: Dict[str, TypeSymbol]             # 类型符号
- methods: Dict[str, MethodSymbol]         # 方法符号
- properties: Dict[str, PropertySymbol]    # 属性符号
- fields: Dict[str, FieldSymbol]           # 字段符号
- inheritance_tree: Dict[str, List[str]]   # 继承关系
- call_graph: Dict[str, List[str]]         # 调用关系
```

### 2. 语义分析器 (SemanticAnalyzer)

主要分析器类：

```python
from src.csharp_assistant.core.services.semantic_analyzer import SemanticAnalyzer

# 创建分析器
analyzer = SemanticAnalyzer(omnisharp_client, config)

# 分析项目
result = await analyzer.analyze_project(project_path)
```

### 3. HTTP API 客户端 (OmniSharpHttpClient)

支持 OmniSharp HTTP API：

```python
from src.csharp_assistant.interfaces.language_server.omnisharp_http_client import OmniSharpHttpClient

# 创建 HTTP 客户端
http_client = OmniSharpHttpClient(config)

# 启动 HTTP 服务器
await http_client.start(project_path, port=2000)

# 获取代码结构
structure = await http_client.get_code_structure()
```

## 使用方法

### 基本使用

```python
import asyncio
from src.csharp_assistant.interfaces.language_server.omnisharp_client import OmniSharpClient
from src.csharp_assistant.core.services.semantic_analyzer import SemanticAnalyzer
from src.csharp_assistant.config.config import get_config

async def analyze_project():
    # 1. 初始化
    config = get_config()
    omnisharp_client = OmniSharpClient()
    analyzer = SemanticAnalyzer(omnisharp_client, config)
    
    # 2. 启动 OmniSharp
    await omnisharp_client.start(config.project_path)
    
    # 3. 分析项目
    result = await analyzer.analyze_project(config.project_path)
    
    # 4. 使用结果
    semantic_model = result.semantic_model
    print(f"找到 {len(semantic_model.symbols)} 个符号")
    
    # 5. 清理
    await omnisharp_client.stop()

# 运行
asyncio.run(analyze_project())
```

### 查询符号信息

```python
# 按类型查询符号
from src.csharp_assistant.core.domain.semantic_model import SymbolKind

classes = semantic_model.get_symbols_by_kind(SymbolKind.CLASS)
methods = semantic_model.get_symbols_by_kind(SymbolKind.METHOD)
properties = semantic_model.get_symbols_by_kind(SymbolKind.PROPERTY)

# 按文件查询符号
file_symbols = semantic_model.get_symbols_in_file("path/to/file.cs")

# 查询特定符号
symbol = semantic_model.get_symbol(symbol_id)

# 查询派生类型
derived_types = semantic_model.get_derived_types(type_id)

# 查询方法调用
called_methods = semantic_model.get_called_methods(method_id)
```

### 分析继承关系

```python
# 遍历继承树
for base_type_id, derived_type_ids in semantic_model.inheritance_tree.items():
    base_symbol = semantic_model.get_symbol(base_type_id)
    print(f"基类: {base_symbol.name}")
    
    for derived_id in derived_type_ids:
        derived_symbol = semantic_model.get_symbol(derived_id)
        print(f"  派生类: {derived_symbol.name}")
```

### 分析调用关系

```python
# 遍历调用图
for caller_id, called_ids in semantic_model.call_graph.items():
    caller_symbol = semantic_model.get_symbol(caller_id)
    print(f"方法: {caller_symbol.name}")
    
    for called_id in called_ids:
        called_symbol = semantic_model.get_symbol(called_id)
        print(f"  调用: {called_symbol.name}")
```

### 分析引用关系

```python
# 查看符号的所有引用
for symbol in semantic_model.symbols.values():
    if symbol.references:
        print(f"符号: {symbol.name}")
        for ref in symbol.references:
            print(f"  引用位置: {ref.location.file_path}:{ref.location.start_line}")
```

## 配置要求

### 1. 环境配置

确保以下环境变量正确设置：

```bash
# .env 文件
PROJECT_PATH=E:\Github\4.2.1\Game-6
LANGUAGE_SERVER_PATH=C:\Users\<USER>\.vscode\extensions\ms-dotnettools.csharp-2.55.29-win32-x64\omnisharp\OmniSharp.exe
LANGUAGE_SERVER_ENABLED=true
```

### 2. 项目要求

- 项目必须是有效的 C# 项目（包含 .csproj 或 .sln 文件）
- 项目能够成功编译（`dotnet build` 无错误）
- Godot SDK 已正确还原

### 3. 验证步骤

```bash
# 1. 还原依赖
dotnet restore

# 2. 编译项目
dotnet build

# 3. 检查 Godot SDK
ls %USERPROFILE%\.nuget\packages\godot.net.sdk\
```

## 运行测试

### 1. 基本测试

```bash
python test_semantic_analyzer.py
```

### 2. 演示脚本

```bash
python demo_semantic_analysis.py
```

### 3. 现有测试

```bash
python test_omnisharp_analysis.py
python test_omnisharp_fix.py
```

## 输出示例

### 分析结果统计

```
📈 分析结果:
   ⏱️  分析耗时: 15.32 秒
   📁 分析文件: 25 个
   🔍 发现符号: 342 个

🏗️ 语义模型详情:
   📦 命名空间: 8
   🏛️  类型: 45
   🔧 方法: 156
   🏷️  属性: 89
   📋 字段: 44
   📡 事件: 0
```

### 符号示例

```
🔍 符号示例:
   📦 类示例:
      🏛️  Game.Player
         📍 位置: Player.cs:10
         🔗 继承: Node2D
      🏛️  Game.Enemy
         📍 位置: Enemy.cs:5
         🔗 继承: CharacterBody2D

   🔧 方法示例:
      ⚙️  Game.Player.Move
         📍 位置: Player.cs:25
         🔄 返回: void
      ⚙️  Game.Enemy.Attack
         📍 位置: Enemy.cs:18
         🔄 返回: bool
```

## 故障排除

### 1. OmniSharp 启动失败

- 检查 `LANGUAGE_SERVER_PATH` 是否正确
- 确保 .NET SDK 已安装
- 检查项目文件是否有效

### 2. 分析结果为空

- 确保项目能够编译
- 检查 Godot SDK 是否正确还原
- 查看错误日志

### 3. HTTP API 不可用

- 检查端口是否被占用
- 确保 OmniSharp 版本支持 HTTP API
- 查看服务器启动日志

## 高级用法

### 1. 自定义分析

```python
# 只分析特定文件
analyzer.use_http_api = False  # 使用 LSP 模式
result = await analyzer._analyze_file_symbols("specific_file.cs")
```

### 2. 扩展符号处理

```python
# 自定义符号处理器
class CustomSymbolProcessor:
    def process_class(self, class_symbol):
        # 自定义类处理逻辑
        pass
    
    def process_method(self, method_symbol):
        # 自定义方法处理逻辑
        pass
```

### 3. 结果导出

```python
# 导出为 JSON
import json

def export_semantic_model(semantic_model, output_file):
    data = {
        "symbols": {sid: symbol_to_dict(s) for sid, s in semantic_model.symbols.items()},
        "inheritance_tree": semantic_model.inheritance_tree,
        "call_graph": semantic_model.call_graph
    }
    
    with open(output_file, 'w') as f:
        json.dump(data, f, indent=2)
```

## 性能优化

### 1. 减少分析范围

```python
# 只分析特定目录
cs_files = list(Path(project_path).glob("Scripts/**/*.cs"))
```

### 2. 并行处理

```python
# 并行分析文件
import asyncio

async def analyze_files_parallel(files):
    tasks = [analyzer._analyze_file_symbols(f) for f in files]
    await asyncio.gather(*tasks)
```

### 3. 缓存结果

```python
# 缓存分析结果
import pickle

def save_cache(semantic_model, cache_file):
    with open(cache_file, 'wb') as f:
        pickle.dump(semantic_model, f)

def load_cache(cache_file):
    with open(cache_file, 'rb') as f:
        return pickle.load(f)
```
