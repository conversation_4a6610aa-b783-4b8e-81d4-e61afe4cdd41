"""
OmniSharp HTTP API 客户端

支持通过 HTTP API 与 OmniSharp 通信，获取更详细的代码结构信息
"""

import asyncio
import aiohttp
import json
import logging
import os
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)


class OmniSharpHttpClient:
    """OmniSharp HTTP API 客户端"""
    
    def __init__(self, config=None):
        """
        初始化 HTTP 客户端
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.base_url = "http://localhost:2000"
        self.process = None
        self.session = None
        self.is_running = False
        self.project_path = None
    
    async def start(self, project_path: str, port: int = 2000) -> bool:
        """
        启动 OmniSharp HTTP 服务器
        
        Args:
            project_path: 项目路径
            port: HTTP 端口
            
        Returns:
            bool: 是否启动成功
        """
        try:
            self.project_path = str(Path(project_path).resolve())
            self.base_url = f"http://localhost:{port}"
            
            logger.info(f"启动 OmniSharp HTTP 服务器: {self.project_path}")
            
            # 检查项目文件
            project_files = list(Path(self.project_path).glob("*.csproj"))
            sln_files = list(Path(self.project_path).glob("*.sln"))
            
            project_file = None
            if sln_files:
                project_file = str(sln_files[0])
                logger.info(f"找到解决方案文件: {project_file}")
            elif project_files:
                project_file = str(project_files[0])
                logger.info(f"找到项目文件: {project_file}")
            else:
                logger.warning("未找到.sln或.csproj文件，使用目录路径")
                project_file = self.project_path
            
            # 设置环境变量
            env = os.environ.copy()
            if not env.get("DOTNET_ROOT"):
                env["DOTNET_ROOT"] = "C:\\Program Files\\dotnet"
            
            env["UseSharedCompilation"] = "false"
            env["DOTNET_CLI_TELEMETRY_OPTOUT"] = "1"
            
            # 启动 OmniSharp HTTP 服务器
            cmd_args = [
                self.config.language_server_path if self.config else "OmniSharp.exe",
                "-s", project_file,
                "-p", str(port),
                "--hostPID", str(os.getpid()),
                "--encoding", "utf-8"
            ]
            
            logger.info(f"启动命令: {' '.join(cmd_args)}")
            
            self.process = subprocess.Popen(
                cmd_args,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env
            )
            
            # 等待服务器启动
            await self._wait_for_server_ready(timeout=30)
            
            # 创建 HTTP 会话
            self.session = aiohttp.ClientSession()
            
            self.is_running = True
            logger.info("OmniSharp HTTP 服务器启动成功")
            return True
            
        except Exception as e:
            logger.error(f"启动 OmniSharp HTTP 服务器失败: {e}")
            return False
    
    async def _wait_for_server_ready(self, timeout: int = 30):
        """等待服务器就绪"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"{self.base_url}/checkalivestatus") as response:
                        if response.status == 200:
                            logger.info("OmniSharp HTTP 服务器就绪")
                            return
            except:
                pass
            
            await asyncio.sleep(1)
        
        raise TimeoutError("等待 OmniSharp HTTP 服务器就绪超时")
    
    async def stop(self):
        """停止 HTTP 服务器"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            
            if self.process:
                self.process.terminate()
                self.process.wait(timeout=10)
                self.process = None
            
            self.is_running = False
            logger.info("OmniSharp HTTP 服务器已停止")
            
        except Exception as e:
            logger.error(f"停止 OmniSharp HTTP 服务器失败: {e}")
    
    async def get_code_structure(self) -> Optional[Dict[str, Any]]:
        """
        获取代码结构
        
        Returns:
            Dict: 代码结构信息
        """
        try:
            if not self.is_running or not self.session:
                logger.error("OmniSharp HTTP 服务器未运行")
                return None
            
            url = f"{self.base_url}/v2/codestructure"
            
            async with self.session.post(url, json={}) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"获取代码结构成功，包含 {len(data.get('Elements', []))} 个元素")
                    return data
                else:
                    logger.error(f"获取代码结构失败: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"获取代码结构失败: {e}")
            return None
    
    async def get_project_information(self) -> Optional[Dict[str, Any]]:
        """
        获取项目信息
        
        Returns:
            Dict: 项目信息
        """
        try:
            if not self.is_running or not self.session:
                logger.error("OmniSharp HTTP 服务器未运行")
                return None
            
            url = f"{self.base_url}/project"
            
            async with self.session.post(url, json={}) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info("获取项目信息成功")
                    return data
                else:
                    logger.error(f"获取项目信息失败: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"获取项目信息失败: {e}")
            return None
    
    async def get_type_lookup(self, type_name: str) -> Optional[Dict[str, Any]]:
        """
        查找类型信息
        
        Args:
            type_name: 类型名称
            
        Returns:
            Dict: 类型信息
        """
        try:
            if not self.is_running or not self.session:
                logger.error("OmniSharp HTTP 服务器未运行")
                return None
            
            url = f"{self.base_url}/typelookup"
            payload = {"Type": type_name}
            
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.debug(f"查找类型 {type_name} 成功")
                    return data
                else:
                    logger.error(f"查找类型 {type_name} 失败: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"查找类型 {type_name} 失败: {e}")
            return None
    
    async def get_members(self, file_path: str, line: int, column: int) -> Optional[Dict[str, Any]]:
        """
        获取成员信息
        
        Args:
            file_path: 文件路径
            line: 行号
            column: 列号
            
        Returns:
            Dict: 成员信息
        """
        try:
            if not self.is_running or not self.session:
                logger.error("OmniSharp HTTP 服务器未运行")
                return None
            
            url = f"{self.base_url}/autocomplete"
            payload = {
                "FileName": file_path,
                "Line": line,
                "Column": column,
                "WordToComplete": "",
                "WantDocumentationForEveryCompletionResult": True,
                "WantImportableTypes": True,
                "WantMethodHeader": True,
                "WantSnippet": True,
                "WantReturnType": True,
                "WantKind": True
            }
            
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.debug(f"获取成员信息成功: {file_path}:{line}:{column}")
                    return data
                else:
                    logger.error(f"获取成员信息失败: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"获取成员信息失败: {e}")
            return None
    
    async def find_usages(self, file_path: str, line: int, column: int) -> Optional[Dict[str, Any]]:
        """
        查找符号使用情况
        
        Args:
            file_path: 文件路径
            line: 行号
            column: 列号
            
        Returns:
            Dict: 使用情况信息
        """
        try:
            if not self.is_running or not self.session:
                logger.error("OmniSharp HTTP 服务器未运行")
                return None
            
            url = f"{self.base_url}/findusages"
            payload = {
                "FileName": file_path,
                "Line": line,
                "Column": column,
                "OnlyThisFile": False,
                "ExcludeDefinition": False
            }
            
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.debug(f"查找使用情况成功: {file_path}:{line}:{column}")
                    return data
                else:
                    logger.error(f"查找使用情况失败: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"查找使用情况失败: {e}")
            return None
    
    async def get_definition(self, file_path: str, line: int, column: int) -> Optional[Dict[str, Any]]:
        """
        获取定义信息
        
        Args:
            file_path: 文件路径
            line: 行号
            column: 列号
            
        Returns:
            Dict: 定义信息
        """
        try:
            if not self.is_running or not self.session:
                logger.error("OmniSharp HTTP 服务器未运行")
                return None
            
            url = f"{self.base_url}/gotodefinition"
            payload = {
                "FileName": file_path,
                "Line": line,
                "Column": column
            }
            
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.debug(f"获取定义信息成功: {file_path}:{line}:{column}")
                    return data
                else:
                    logger.error(f"获取定义信息失败: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"获取定义信息失败: {e}")
            return None
    
    async def get_signature_help(self, file_path: str, line: int, column: int) -> Optional[Dict[str, Any]]:
        """
        获取签名帮助
        
        Args:
            file_path: 文件路径
            line: 行号
            column: 列号
            
        Returns:
            Dict: 签名帮助信息
        """
        try:
            if not self.is_running or not self.session:
                logger.error("OmniSharp HTTP 服务器未运行")
                return None
            
            url = f"{self.base_url}/signaturehelp"
            payload = {
                "FileName": file_path,
                "Line": line,
                "Column": column
            }
            
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.debug(f"获取签名帮助成功: {file_path}:{line}:{column}")
                    return data
                else:
                    logger.error(f"获取签名帮助失败: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"获取签名帮助失败: {e}")
            return None
